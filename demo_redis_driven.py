#!/usr/bin/env python3
"""
演示Redis驱动的预约机制
"""

import json
import time
import threading
from user_manager import redis_client

def demo_redis_driven_mechanism():
    """演示Redis驱动的预约机制"""
    print("=== Redis驱动的预约机制演示 ===\n")
    
    # 模拟通知数据
    notifications = [
        {
            "name": "广州旅游签证普通号",
            "data": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": False, "dates": ["2025-07-25"]},
            "redis_key": "spain-GUANGZHOU-Tourism-False"
        },
        {
            "name": "广州旅游签证VIP号", 
            "data": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": True, "dates": ["2025-07-25"]},
            "redis_key": "spain-GUANGZHOU-Tourism-True"
        },
        {
            "name": "北京商务签证普通号",
            "data": {"centerCode": "BEIJING", "visaType": "Business", "isVIP": False, "dates": ["2025-07-26"]},
            "redis_key": "spain-BEIJING-Business-False"
        }
    ]
    
    print("1. 设置Redis值，模拟放号通知")
    for notif in notifications:
        redis_client.set(notif["redis_key"], json.dumps(notif["data"]), 60)
        print(f"   设置 {notif['redis_key']} = {notif['name']}")
    
    print("\n2. 模拟接收通知和去重检查")
    for notif in notifications:
        redis_value = redis_client.get(notif["redis_key"])
        if redis_value:
            print(f"   ✓ {notif['name']}: Redis有值，开始处理")
        else:
            print(f"   ✗ {notif['name']}: Redis无值，忽略通知")
    
    print("\n3. 模拟动态循环预约")
    
    # 选择第一个通知进行详细演示
    demo_notif = notifications[0]
    redis_key = demo_notif["redis_key"]
    
    print(f"\n演示通知: {demo_notif['name']}")
    print(f"Redis键: {redis_key}")
    
    def simulate_appointment_loop():
        """模拟预约循环"""
        round_num = 0
        while True:
            redis_value = redis_client.get(redis_key)
            if not redis_value:
                print(f"   第 {round_num + 1} 轮: Redis值为空，停止预约")
                break
            
            round_num += 1
            print(f"   第 {round_num} 轮: 开始预约，Redis值存在")
            
            # 模拟预约处理时间
            time.sleep(1)
            
            # 模拟在第3轮时删除Redis值
            if round_num == 3:
                print("     → 模拟外部删除Redis值（放号结束）")
                redis_client.delete(redis_key)
            
            time.sleep(1)  # 检查间隔
        
        print(f"   预约循环结束，总共执行 {round_num} 轮")
    
    # 启动模拟循环
    simulate_appointment_loop()
    
    print("\n4. 清理演示数据")
    for notif in notifications:
        redis_client.delete(notif["redis_key"])
        print(f"   删除 {notif['redis_key']}")

def demo_concurrent_notifications():
    """演示并发通知处理"""
    print("\n\n=== 并发通知处理演示 ===\n")
    
    # 准备多个通知
    notifications = [
        {"key": "spain-GUANGZHOU-Tourism-False", "name": "广州旅游普通", "duration": 5},
        {"key": "spain-GUANGZHOU-Tourism-True", "name": "广州旅游VIP", "duration": 3},
        {"key": "spain-BEIJING-Business-False", "name": "北京商务普通", "duration": 4}
    ]
    
    def process_notification(notif):
        """处理单个通知"""
        redis_key = notif["key"]
        name = notif["name"]
        duration = notif["duration"]
        
        # 设置Redis值
        redis_client.set(redis_key, f"data-{name}", 60)
        print(f"[{name}] 开始处理")
        
        round_num = 0
        start_time = time.time()
        
        while time.time() - start_time < duration:
            redis_value = redis_client.get(redis_key)
            if not redis_value:
                print(f"[{name}] Redis值为空，停止")
                break
            
            round_num += 1
            print(f"[{name}] 第 {round_num} 轮预约")
            time.sleep(1)
        
        # 清理
        redis_client.delete(redis_key)
        print(f"[{name}] 处理完成，共 {round_num} 轮")
    
    # 启动并发处理
    threads = []
    for notif in notifications:
        thread = threading.Thread(target=process_notification, args=(notif,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("\n所有并发通知处理完成")

def demo_redis_key_format():
    """演示Redis键格式"""
    print("\n\n=== Redis键格式演示 ===\n")
    
    test_cases = [
        {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": False},
        {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": True},
        {"centerCode": "BEIJING", "visaType": "Business", "isVIP": False},
        {"centerCode": "SHANGHAI", "visaType": "Student", "isVIP": True},
    ]
    
    print("通知信息 → Redis键格式:")
    for case in test_cases:
        redis_key = f"spain-{case['centerCode']}-{case['visaType']}-{case['isVIP']}"
        vip_text = "VIP" if case['isVIP'] else "普通"
        print(f"  {case['centerCode']} {case['visaType']} {vip_text} → {redis_key}")

if __name__ == "__main__":
    try:
        demo_redis_driven_mechanism()
        demo_concurrent_notifications()
        demo_redis_key_format()
        print("\n🎉 演示完成！")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
