#!/usr/bin/env python3
"""
测试扫描优化功能
"""

import time
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_spain_faker_users_appointment_v2 import (
    ScanStatus, SCAN_INTERVAL, SUCCESS_PAUSE, RETRY_INTERVAL, MAX_RETRY_COUNT,
    get_regions_info, scan_job_with_retry
)

def test_scan_status():
    """测试扫描状态管理"""
    print("=== 测试扫描状态管理 ===\n")
    
    status = ScanStatus()
    region = "GUANGZHOU-Tourism"
    
    # 测试初始状态
    print("1. 测试初始状态:")
    should_scan = status.should_scan_region(region)
    print(f"   初始应该扫描: {should_scan} (期望: True)")
    assert should_scan == True
    
    # 测试开始扫描
    print("\n2. 测试开始扫描:")
    status.start_scanning(region)
    should_scan_again = status.should_scan_region(region)
    print(f"   正在扫描时应该扫描: {should_scan_again} (期望: False)")
    assert should_scan_again == False
    
    # 测试扫描完成（失败）
    print("\n3. 测试扫描失败:")
    status.finish_scanning(region, False)
    retry_count = status.region_retry_count.get(region, 0)
    should_retry = status.should_retry(region)
    print(f"   失败后重试次数: {retry_count} (期望: 1)")
    print(f"   应该重试: {should_retry} (期望: True)")
    assert retry_count == 1
    assert should_retry == True
    
    # 测试扫描完成（成功）
    print("\n4. 测试扫描成功:")
    status.finish_scanning(region, True)
    retry_count_after_success = status.region_retry_count.get(region, 0)
    print(f"   成功后重试次数: {retry_count_after_success} (期望: 0)")
    assert retry_count_after_success == 0
    
    # 测试成功后的暂停
    print("\n5. 测试成功后暂停:")
    should_scan_after_success = status.should_scan_region(region)
    print(f"   成功后立即扫描: {should_scan_after_success} (期望: False)")
    assert should_scan_after_success == False
    
    print("   ✓ 扫描状态管理测试通过\n")

def test_scan_intervals():
    """测试扫描间隔"""
    print("=== 测试扫描间隔 ===\n")
    
    status = ScanStatus()
    region = "BEIJING-Business"
    
    # 模拟时间
    current_time = time.time()
    
    # 设置最后扫描时间为5分钟前
    status.region_last_scan[region] = current_time - 5 * 60
    
    should_scan = status.should_scan_region(region)
    print(f"5分钟前扫描过，现在应该扫描: {should_scan} (期望: False，因为间隔是30分钟)")
    assert should_scan == False
    
    # 设置最后扫描时间为35分钟前
    status.region_last_scan[region] = current_time - 35 * 60
    
    should_scan = status.should_scan_region(region)
    print(f"35分钟前扫描过，现在应该扫描: {should_scan} (期望: True)")
    assert should_scan == True
    
    print("   ✓ 扫描间隔测试通过\n")

def test_success_pause():
    """测试成功后暂停"""
    print("=== 测试成功后暂停 ===\n")
    
    status = ScanStatus()
    region = "SHANGHAI-Tourism"
    
    current_time = time.time()
    
    # 设置最后成功时间为10分钟前
    status.region_last_success[region] = current_time - 10 * 60
    
    should_scan = status.should_scan_region(region)
    print(f"10分钟前成功过，现在应该扫描: {should_scan} (期望: False，因为成功暂停是30分钟)")
    assert should_scan == False
    
    # 设置最后成功时间为35分钟前
    status.region_last_success[region] = current_time - 35 * 60
    
    should_scan = status.should_scan_region(region)
    print(f"35分钟前成功过，现在应该扫描: {should_scan} (期望: True)")
    assert should_scan == True
    
    print("   ✓ 成功后暂停测试通过\n")

def test_retry_mechanism():
    """测试重试机制"""
    print("=== 测试重试机制 ===\n")
    
    status = ScanStatus()
    region = "CHENGDU-Business"
    
    # 测试重试次数限制
    for i in range(MAX_RETRY_COUNT + 2):
        status.region_retry_count[region] = i
        should_retry = status.should_retry(region)
        expected = i < MAX_RETRY_COUNT
        print(f"重试次数 {i}，应该重试: {should_retry} (期望: {expected})")
        assert should_retry == expected
    
    print("   ✓ 重试机制测试通过\n")

def test_get_regions_info():
    """测试获取地区信息"""
    print("=== 测试获取地区信息 ===\n")

    # Mock用户数据
    mock_spain_users = [
        {"centerCode": "GUANGZHOU", "visaTypeCode": "Tourism"},
        {"centerCode": "BEIJING", "visaTypeCode": "Business"},
    ]

    mock_faker_users = [
        {"centerCode": "GUANGZHOU", "visaTypeCode": "Tourism"},
        {"centerCode": "SHANGHAI", "visaTypeCode": "Tourism"},
        {"centerCode": "CHENGDU", "visaTypeCode": "Business"},
    ]

    with patch('main_spain_faker_users_appointment_v2.get_users_with_queue_name') as mock_get_users:
        def side_effect(queue_name):
            if queue_name == 'spainUserDatas':  # spain_user_field
                return mock_spain_users
            else:  # spain_faker_scaning
                return mock_faker_users

        mock_get_users.side_effect = side_effect

        all_regions, real_user_regions = get_regions_info()

        print(f"所有地区: {all_regions}")
        print(f"有真实用户的地区: {real_user_regions}")

        expected_all_regions = {
            "GUANGZHOU-Tourism",
            "BEIJING-Business",
            "SHANGHAI-Tourism",
            "CHENGDU-Business"
        }

        expected_real_regions = {
            "GUANGZHOU-Tourism",
            "BEIJING-Business"
        }

        all_regions_set = set(all_regions)
        print(f"期望所有地区: {expected_all_regions}")
        print(f"实际所有地区: {all_regions_set}")
        print(f"所有地区匹配: {all_regions_set == expected_all_regions}")

        print(f"期望真实用户地区: {expected_real_regions}")
        print(f"实际真实用户地区: {real_user_regions}")
        print(f"真实用户地区匹配: {real_user_regions == expected_real_regions}")

        assert all_regions_set == expected_all_regions
        assert real_user_regions == expected_real_regions

    print("   ✓ 获取地区信息测试通过\n")

def test_scan_with_retry():
    """测试带重试的扫描"""
    print("=== 测试带重试的扫描 ===\n")
    
    # Mock用户
    mock_user = {
        "centerCode": "TEST",
        "visaTypeCode": "Tourism",
        "email": "<EMAIL>"
    }
    
    # 测试成功场景
    print("1. 测试扫描成功:")
    with patch('main_spain_faker_users_appointment_v2.scan_job', return_value=(True, ["2025-07-25"])):
        success, result = scan_job_with_retry(mock_user, "TEST-Tourism")
        print(f"   扫描结果: 成功={success}, 结果={result}")
        assert success == True
        assert result == ["2025-07-25"]
    
    # 测试失败但有重试
    print("\n2. 测试扫描失败重试:")
    call_count = 0
    def failing_scan_job(user):
        nonlocal call_count
        call_count += 1
        if call_count < 2:  # 第一次失败
            return False, None
        else:  # 第二次成功
            return True, []
    
    with patch('main_spain_faker_users_appointment_v2.scan_job', side_effect=failing_scan_job):
        with patch('time.sleep'):  # 跳过实际的sleep
            success, result = scan_job_with_retry(mock_user, "TEST-Tourism")
            print(f"   重试后结果: 成功={success}, 调用次数={call_count}")
            assert success == True
            assert call_count == 2
    
    print("   ✓ 带重试扫描测试通过\n")

def test_real_vs_fake_user_regions():
    """测试真实用户地区和虚拟用户地区的区别处理"""
    print("=== 测试真实用户地区 vs 虚拟用户地区 ===\n")

    status = ScanStatus()
    real_region = "GUANGZHOU-Tourism"
    fake_region = "SHANGHAI-Tourism"

    # 测试有真实用户的地区扫描间隔（5分钟）
    print("1. 测试有真实用户地区的扫描间隔:")
    current_time = time.time()

    # 设置3分钟前扫描过
    status.region_last_scan[real_region] = current_time - 3 * 60
    should_scan = status.should_scan_region(real_region, has_real_users=True)
    print(f"   有真实用户地区，3分钟前扫描过，现在应该扫描: {should_scan} (期望: False)")
    assert should_scan == False

    # 设置6分钟前扫描过
    status.region_last_scan[real_region] = current_time - 6 * 60
    should_scan = status.should_scan_region(real_region, has_real_users=True)
    print(f"   有真实用户地区，6分钟前扫描过，现在应该扫描: {should_scan} (期望: True)")
    assert should_scan == True

    # 测试无真实用户的地区扫描间隔（30分钟）
    print("\n2. 测试无真实用户地区的扫描间隔:")

    # 设置10分钟前扫描过
    status.region_last_scan[fake_region] = current_time - 10 * 60
    should_scan = status.should_scan_region(fake_region, has_real_users=False)
    print(f"   无真实用户地区，10分钟前扫描过，现在应该扫描: {should_scan} (期望: False)")
    assert should_scan == False

    # 设置35分钟前扫描过
    status.region_last_scan[fake_region] = current_time - 35 * 60
    should_scan = status.should_scan_region(fake_region, has_real_users=False)
    print(f"   无真实用户地区，35分钟前扫描过，现在应该扫描: {should_scan} (期望: True)")
    assert should_scan == True

    # 测试无真实用户地区成功后的暂停
    print("\n3. 测试无真实用户地区成功后暂停:")
    status.region_last_success[fake_region] = current_time - 10 * 60
    should_scan = status.should_scan_region(fake_region, has_real_users=False)
    print(f"   无真实用户地区，10分钟前成功过，现在应该扫描: {should_scan} (期望: False)")
    assert should_scan == False

    # 测试有真实用户地区不受成功暂停影响
    print("\n4. 测试有真实用户地区不受成功暂停影响:")
    status.region_last_success[real_region] = current_time - 10 * 60
    status.region_last_scan[real_region] = current_time - 6 * 60
    should_scan = status.should_scan_region(real_region, has_real_users=True)
    print(f"   有真实用户地区，10分钟前成功过，6分钟前扫描过，现在应该扫描: {should_scan} (期望: True)")
    assert should_scan == True

    print("   ✓ 真实用户地区 vs 虚拟用户地区测试通过\n")

def test_configuration():
    """测试配置参数"""
    print("=== 测试配置参数 ===\n")
    
    print(f"扫描间隔: {SCAN_INTERVAL} 秒 ({SCAN_INTERVAL//60} 分钟)")
    print(f"成功暂停: {SUCCESS_PAUSE} 秒 ({SUCCESS_PAUSE//60} 分钟)")
    print(f"重试间隔: {RETRY_INTERVAL} 秒 ({RETRY_INTERVAL//60} 分钟)")
    print(f"最大重试次数: {MAX_RETRY_COUNT}")
    
    # 验证配置合理性
    assert SCAN_INTERVAL == 30 * 60, "扫描间隔应该是30分钟"
    assert SUCCESS_PAUSE == 30 * 60, "成功暂停应该是30分钟"
    assert RETRY_INTERVAL == 5 * 60, "重试间隔应该是5分钟"
    assert MAX_RETRY_COUNT == 3, "最大重试次数应该是3"
    
    print("   ✓ 配置参数测试通过\n")

if __name__ == "__main__":
    try:
        test_scan_status()
        test_scan_intervals()
        test_success_pause()
        test_retry_mechanism()
        test_get_regions_info()
        test_scan_with_retry()
        test_real_vs_fake_user_regions()
        test_configuration()
        print("🎉 所有扫描优化测试都通过了！")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
