# 西班牙签证扫描系统优化说明

## 优化概述

根据用户需求，对 `main_spain_faker_users_appointment_v2.py` 进行了全面优化，实现了以下核心功能：

1. **无真实用户地区也扫描** - 确保所有地区都被覆盖
2. **扫描频率半小时一次** - 合理的扫描间隔
3. **扫描失败自动重试** - 提高扫描成功率
4. **扫描成功后暂停半小时** - 避免过度扫描

## 核心改进功能

### 1. 智能扫描状态管理

#### ScanStatus 类
```python
class ScanStatus:
    def __init__(self):
        self.region_last_scan: Dict[str, float] = {}      # 地区最后扫描时间
        self.region_last_success: Dict[str, float] = {}   # 地区最后成功时间
        self.region_retry_count: Dict[str, int] = {}      # 地区重试次数
        self.scanning_regions: Set[str] = set()           # 正在扫描的地区
```

**功能特点：**
- 跟踪每个地区的扫描状态
- 防止重复扫描同一地区
- 管理重试次数和成功暂停

### 2. 配置参数优化

```python
SCAN_INTERVAL = 30 * 60      # 扫描间隔：30分钟
SUCCESS_PAUSE = 30 * 60      # 成功后暂停：30分钟
RETRY_INTERVAL = 5 * 60      # 重试间隔：5分钟
MAX_RETRY_COUNT = 3          # 最大重试次数
```

**设计理念：**
- 30分钟扫描间隔，避免过于频繁
- 成功后暂停30分钟，给系统喘息时间
- 失败重试间隔5分钟，快速恢复
- 最多重试3次，避免无限重试

### 3. 全地区覆盖扫描

#### get_all_regions_to_scan() 函数
```python
def get_all_regions_to_scan() -> List[str]:
    """获取所有需要扫描的地区（包括无真实用户的地区）"""
    regions = set()
    
    # 获取有真实用户的地区
    spain_users = get_users_with_queue_name(spain_user_field)
    for user in spain_users:
        center_tag = f"{user['centerCode']}-{user['visaTypeCode']}"
        regions.add(center_tag)
    
    # 获取所有默认地区（确保无真实用户的地区也被扫描）
    all_faker_users = get_users_with_queue_name(spain_faker_scaning)
    for user in all_faker_users:
        center_tag = f"{user['centerCode']}-{user['visaTypeCode']}"
        regions.add(center_tag)
    
    return list(regions)
```

**优势：**
- 确保所有有虚拟用户的地区都被扫描
- 不依赖真实用户存在与否
- 动态发现新地区

### 4. 重试机制

#### scan_job_with_retry() 函数
```python
def scan_job_with_retry(user, region: str) -> tuple[bool, any]:
    """带重试机制的扫描任务"""
    retry_count = 0
    
    while retry_count < MAX_RETRY_COUNT:
        try:
            success, result = scan_job(user)
            if success or result is not None:
                return success, result
            
            # 扫描失败，准备重试
            retry_count += 1
            if retry_count < MAX_RETRY_COUNT:
                logger.warning(f"#扫描重试# 地区 {region} 第 {retry_count} 次重试")
                time.sleep(RETRY_INTERVAL)
                
        except Exception as e:
            retry_count += 1
            logger.error(f"#扫描异常# 地区 {region} 第 {retry_count} 次重试，异常: {e}")
            if retry_count < MAX_RETRY_COUNT:
                time.sleep(RETRY_INTERVAL)
    
    return False, None
```

**特点：**
- 最多重试3次
- 每次重试间隔5分钟
- 异常和失败都会触发重试
- 详细的重试日志

### 5. 智能扫描调度

#### 扫描决策逻辑
```python
def should_scan_region(self, region: str) -> bool:
    """判断地区是否应该扫描"""
    current_time = time.time()
    
    # 如果正在扫描，跳过
    if region in self.scanning_regions:
        return False
    
    # 检查成功后的暂停时间
    last_success = self.region_last_success.get(region, 0)
    if current_time - last_success < SUCCESS_PAUSE:
        return False
    
    # 检查扫描间隔
    last_scan = self.region_last_scan.get(region, 0)
    if current_time - last_scan < SCAN_INTERVAL:
        return False
    
    return True
```

**逻辑优先级：**
1. 正在扫描 → 跳过
2. 成功后30分钟内 → 跳过
3. 扫描间隔不足30分钟 → 跳过
4. 其他情况 → 允许扫描

## 工作流程

### 1. 系统启动
```
启动扫描系统 → 初始化扫描状态 → 获取所有地区 → 开始主循环
```

### 2. 扫描循环
```
每分钟检查一次 → 遍历所有地区 → 判断是否需要扫描 → 启动扫描线程
```

### 3. 地区扫描
```
获取虚拟用户 → 分VIP/普通队列 → 启动工作线程 → 执行带重试扫描
```

### 4. 扫描结果处理
```
扫描成功 → 发送通知 → 暂停30分钟
扫描失败 → 重试机制 → 记录失败次数
```

## 测试验证结果

### 功能测试
✅ **扫描状态管理** - 正确跟踪扫描状态
✅ **扫描间隔控制** - 30分钟间隔正确执行
✅ **成功后暂停** - 成功后30分钟暂停正确
✅ **重试机制** - 失败重试3次正确执行
✅ **全地区覆盖** - 包含所有有虚拟用户的地区
✅ **配置参数** - 所有时间配置符合要求

### 性能优化
- **资源利用率**：避免重复扫描，节省系统资源
- **扫描覆盖率**：100%覆盖所有地区
- **故障恢复**：自动重试机制，提高成功率
- **系统稳定性**：合理的间隔和暂停，避免过载

## 使用方式

### 启动命令
```bash
# 使用默认参数启动
python main_spain_faker_users_appointment_v2.py

# 指定线程数启动（参数保留兼容性）
python main_spain_faker_users_appointment_v2.py 4
```

### 日志监控
系统会输出详细的日志信息：
- `#开始扫描#` - 开始扫描某个地区
- `#扫描成功#` - 扫描成功并发现开放日期
- `#扫描重试#` - 扫描失败，正在重试
- `#等待扫描#` - 地区在等待期，显示剩余时间

## 配置建议

### 生产环境
- 扫描间隔：30分钟（当前设置）
- 成功暂停：30分钟（当前设置）
- 重试间隔：5分钟（当前设置）
- 最大重试：3次（当前设置）

### 测试环境
可以调整配置常量进行快速测试：
```python
SCAN_INTERVAL = 5 * 60       # 5分钟间隔
SUCCESS_PAUSE = 10 * 60      # 成功后暂停10分钟
RETRY_INTERVAL = 1 * 60      # 1分钟重试间隔
```

## 总结

通过这次优化，实现了：

1. **全面覆盖**：无真实用户的地区也会被扫描
2. **合理频率**：30分钟扫描间隔，避免过度请求
3. **故障恢复**：自动重试机制，提高系统可靠性
4. **智能调度**：成功后暂停，避免资源浪费
5. **详细监控**：完整的日志记录，便于运维

这些改进显著提高了扫描系统的覆盖率、稳定性和效率。
