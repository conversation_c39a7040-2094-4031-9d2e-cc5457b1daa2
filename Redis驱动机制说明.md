# Redis驱动的预约机制说明

## 核心改进

### 原版本（固定轮数）
- 每次通知固定进行3轮预约
- 使用时间窗口进行去重（60秒）
- 预约轮数不可动态调整

### 新版本（Redis驱动）
- 根据Redis值动态控制预约轮数
- 基于Redis值存在性进行去重
- 循环读取直到Redis值为空才停止

## 技术实现

### 1. Redis键格式
```
spain-{centerCode}-{visaType}-{isVIP}
```

**示例：**
- `spain-GUANGZHOU-Tourism-False` (广州旅游签证普通号)
- `spain-GUANGZHOU-Tourism-True` (广州旅游签证VIP号)
- `spain-BEIJING-Business-False` (北京商务签证普通号)

### 2. 去重机制
```python
def _is_duplicate_notification(self, notification_hash: str, current_time: float) -> bool:
    redis_key = f"spain-{notification_hash}"
    redis_value = redis_client.get(redis_key)
    
    if not redis_value:
        return True  # Redis中没有值，忽略通知
    
    # 检查是否正在处理
    if notification_hash in self.active_notifications:
        return True
    
    return False
```

### 3. 动态循环控制
```python
def _execute_multi_rounds(self):
    redis_key = f"spain-{center_code}-{visa_type}-{is_vip}"
    round_num = 0
    
    while self.running:
        redis_value = redis_client.get(redis_key)
        if not redis_value:
            break  # Redis值为空，停止预约
        
        round_num += 1
        # 执行预约
        self._execute_single_round(round_num)
        time.sleep(REDIS_CHECK_INTERVAL)
```

## 工作流程

### 1. 接收通知
```
Redis消息 → 解析centerCode/visaType/isVIP → 生成通知标识
```

### 2. 去重检查
```
检查 spain-{标识} 是否存在 → 不存在则忽略 → 存在则处理
```

### 3. 动态循环
```
while Redis值存在:
    执行一轮预约
    等待2秒
    重新检查Redis值
```

### 4. 自动停止
```
Redis值被删除 → 循环自动停止 → 清理资源
```

## 优势对比

| 特性 | 原版本 | 新版本 |
|------|--------|--------|
| 预约轮数 | 固定3轮 | 动态控制 |
| 停止条件 | 轮数用完 | Redis值为空 |
| 去重机制 | 时间窗口 | Redis值存在性 |
| 资源控制 | 时间控制 | 外部控制 |
| 灵活性 | 低 | 高 |

## 实际应用场景

### 场景1：正常放号
1. 外部系统设置 `spain-GUANGZHOU-Tourism-False` = 放号信息
2. 预约系统接收通知，开始循环预约
3. 外部系统删除Redis值，预约自动停止

### 场景2：重复通知
1. 第一个通知开始处理
2. 第二个相同通知到达，检查Redis值已在处理中
3. 第二个通知被忽略

### 场景3：并发处理
1. 多个不同类型的通知同时到达
2. 每个通知有独立的Redis键
3. 并发处理，互不干扰

## 配置参数

```python
REDIS_CHECK_INTERVAL = 2        # Redis值检查间隔（秒）
ROUND_INTERVAL = 5              # 每轮预约之间的间隔（秒）
WORKER_THREAD_COUNT = 5         # 每个通知的工作线程数
```

## 测试验证

✅ **通知标识生成测试** - 正确生成Redis键格式
✅ **Redis驱动去重测试** - 正确基于Redis值判断
✅ **动态循环控制测试** - 正确根据Redis值控制循环
✅ **并发处理测试** - 多个通知独立处理

## 总结

新的Redis驱动机制提供了更灵活、更可控的预约处理方式：

1. **外部控制**：通过Redis值控制预约的开始和停止
2. **动态调整**：预约轮数根据实际需要动态调整
3. **资源优化**：避免不必要的预约循环
4. **更好的集成**：与外部系统更好地集成

这种机制特别适合需要精确控制预约时机和持续时间的场景。
