# 西班牙签证预约系统 - 优化版本

## 核心功能

### 1. 多Redis通知并发处理
- 为每个Redis通知创建独立的处理器
- 不同通知完全隔离，互不干扰
- 支持真正的并发处理

### 2. 多轮预约机制
- 每次通知进行多轮预约（默认3轮）
- 每轮间隔5秒，提高预约成功率
- 每轮重新获取符合条件的用户

### 3. 智能去重机制
- 基于消息内容生成唯一标识
- 60秒内相同通知只处理一次
- 自动清理过期记录

### 4. 高效资源管理
- 使用线程池管理并发
- 自动资源清理
- 完整的统计信息

## 使用方法

### 启动预约系统
```bash
python main_spain_users_date_app_optimeized.py
# 或
python main_spain_users_date_app_optimeized.py start
```

### 测试单个地区
```bash
python main_spain_users_date_app_optimeized.py test GUANGZHOU
```

## 配置参数

```python
APPOINTMENT_ROUNDS = 3          # 每次通知进行的预约轮数
ROUND_INTERVAL = 5              # 每轮预约之间的间隔（秒）
WORKER_THREAD_COUNT = 5         # 每个通知的工作线程数
NOTIFICATION_DEDUP_WINDOW = 60  # 通知去重时间窗口（秒）
```

## 系统优势

1. **并发处理**：不同Redis通知完全独立，互不干扰
2. **提高成功率**：多轮预约机制增加成功机会
3. **智能去重**：避免重复处理，节省系统资源
4. **资源优化**：线程池管理，高效利用资源
5. **监控完善**：详细的统计信息和日志

## 去重机制

### 唯一标识生成
- 基于 `centerCode`、`visaType`、`isVIP` 三个字段
- 生成MD5哈希值作为唯一标识
- **dates字段不影响去重判断**

### 去重规则
- 60秒内相同标识的通知只处理一次
- 相同地区+相同签证类型+相同VIP状态 = 重复通知
- 日期不同但三个关键字段相同会被去重
- 自动清理过期记录

### 实际效果
```
广州旅游签证普通号 + 任何日期 → 同一个标识
广州旅游签证VIP号 + 任何日期   → 不同标识
广州商务签证普通号 + 任何日期 → 不同标识
北京旅游签证普通号 + 任何日期 → 不同标识
```

## 系统架构

```
Redis消息 → 去重检查 → 创建独立管理器 → 多轮预约 → 统计清理
```

## 注意事项

- 确保Redis连接稳定
- 监控系统资源使用
- 根据需求调整配置参数
