# 西班牙签证预约系统 - 优化版本

## 核心功能

### 1. 多Redis通知并发处理
- 为每个Redis通知创建独立的处理器
- 不同通知完全隔离，互不干扰
- 支持真正的并发处理

### 2. 动态循环预约机制
- 基于Redis值动态控制预约轮数
- 循环读取Redis键直到值为空才停止
- 每轮间隔2秒，持续监控Redis状态

### 3. Redis值驱动的去重机制
- 基于 `centerCode`、`visaType`、`isVIP` 三个字段
- 只有Redis中存在对应值才处理通知
- 不存在Redis值的通知直接忽略

### 4. 高效资源管理
- 使用线程池管理并发
- 自动资源清理
- 完整的统计信息

## 使用方法

### 启动预约系统
```bash
python main_spain_users_date_app_optimeized.py
# 或
python main_spain_users_date_app_optimeized.py start
```

### 测试单个地区
```bash
python main_spain_users_date_app_optimeized.py test GUANGZHOU
```

## 配置参数

```python
ROUND_INTERVAL = 5              # 每轮预约之间的间隔（秒）
REDIS_CHECK_INTERVAL = 2        # Redis值检查间隔（秒）
WORKER_THREAD_COUNT = 5         # 每个通知的工作线程数
```

## 系统优势

1. **并发处理**：不同Redis通知完全独立，互不干扰
2. **提高成功率**：多轮预约机制增加成功机会
3. **智能去重**：避免重复处理，节省系统资源
4. **资源优化**：线程池管理，高效利用资源
5. **监控完善**：详细的统计信息和日志

## 核心机制

### 动态循环控制
- 根据Redis键值动态控制预约轮数
- Redis键格式：`spain-{centerCode}-{visaType}-{isVIP}`
- 循环读取Redis值，直到为空才停止
- 每轮间隔2秒检查Redis状态

### Redis驱动的去重
- 只处理Redis中存在对应值的通知
- 不存在Redis值 = 忽略通知
- 正在处理的通知会被去重

### 实际效果
```
Redis键: spain-GUANGZHOU-Tourism-False
- 有值: 开始预约循环
- 无值: 忽略通知
- 循环中值被删除: 停止预约
```

## 系统架构

```
Redis消息 → 去重检查 → 创建独立管理器 → 多轮预约 → 统计清理
```

## 注意事项

- 确保Redis连接稳定
- 监控系统资源使用
- 根据需求调整配置参数
