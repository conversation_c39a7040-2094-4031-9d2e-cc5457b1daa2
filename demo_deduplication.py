#!/usr/bin/env python3
"""
演示Redis通知去重功能的效果
"""

import json
import time
import threading
from main_spain_users_date_app_optimeized import RedisSubscriber

def simulate_redis_notifications():
    """模拟Redis通知"""
    
    # 创建订阅器
    subscriber = RedisSubscriber()
    
    # 模拟相同的通知消息
    notification_msg = {
        "centerCode": "GUANGZHOU",
        "visaType": "Tourism", 
        "isVIP": False,
        "dates": ["2025-07-25", "2025-07-26", "2025-07-27"]
    }
    
    msg_str = json.dumps(notification_msg)
    
    print("=== Redis通知去重演示 ===\n")
    print(f"模拟通知消息: {msg_str}\n")
    
    # 模拟处理函数
    def mock_process_notification(notification_id, msg_str, notification_hash):
        print(f"  → 开始处理通知 {notification_id} (hash: {notification_hash[:8]}...)")
        time.sleep(2)  # 模拟处理时间
        print(f"  ← 完成处理通知 {notification_id}")
    
    # 替换原始处理函数
    original_process = subscriber._process_notification
    subscriber._process_notification = mock_process_notification
    
    print("1. 发送第一个通知...")
    subscriber._handle_redis_message("test_channel", msg_str)
    time.sleep(0.5)
    
    print("\n2. 立即发送相同通知（应该被去重）...")
    subscriber._handle_redis_message("test_channel", msg_str)
    time.sleep(0.5)
    
    print("\n3. 再次发送相同通知（应该被去重）...")
    subscriber._handle_redis_message("test_channel", msg_str)
    time.sleep(0.5)
    
    print("\n4. 发送不同地区的通知（应该被处理）...")
    different_msg = notification_msg.copy()
    different_msg["centerCode"] = "BEIJING"
    different_msg_str = json.dumps(different_msg)
    subscriber._handle_redis_message("test_channel", different_msg_str)
    time.sleep(0.5)
    
    print("\n5. 发送相同日期但不同顺序的通知（应该被去重）...")
    reordered_msg = notification_msg.copy()
    reordered_msg["dates"] = ["2025-07-27", "2025-07-25", "2025-07-26"]  # 不同顺序
    reordered_msg_str = json.dumps(reordered_msg)
    subscriber._handle_redis_message("test_channel", reordered_msg_str)
    
    # 等待处理完成
    print("\n等待所有处理完成...")
    time.sleep(3)
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    print(f"已处理通知数量: {len(subscriber.processed_notifications)}")
    print(f"活跃通知数量: {len(subscriber.active_notifications)}")
    
    print("\n已处理通知的Hash:")
    for i, (hash_key, timestamp) in enumerate(subscriber.processed_notifications.items(), 1):
        print(f"  {i}. {hash_key} (时间: {time.ctime(timestamp)})")

def demonstrate_time_window():
    """演示时间窗口去重"""
    print("\n\n=== 时间窗口去重演示 ===\n")
    
    subscriber = RedisSubscriber()
    
    notification_msg = {
        "centerCode": "SHANGHAI",
        "visaType": "Business",
        "isVIP": True,
        "dates": ["2025-07-28", "2025-07-29"]
    }
    
    msg_str = json.dumps(notification_msg)
    hash_key = subscriber._generate_notification_hash(msg_str)
    
    print(f"通知Hash: {hash_key}")
    
    # 第一次处理
    current_time = time.time()
    is_dup1 = subscriber._is_duplicate_notification(hash_key, current_time)
    print(f"第一次检查是否重复: {is_dup1}")
    
    # 记录处理
    subscriber.processed_notifications[hash_key] = current_time
    
    # 30秒后检查
    future_time1 = current_time + 30
    is_dup2 = subscriber._is_duplicate_notification(hash_key, future_time1)
    print(f"30秒后检查是否重复: {is_dup2}")
    
    # 70秒后检查（超过60秒窗口）
    future_time2 = current_time + 70
    is_dup3 = subscriber._is_duplicate_notification(hash_key, future_time2)
    print(f"70秒后检查是否重复: {is_dup3}")
    
    print("\n说明:")
    print("- 第一次处理: False (不是重复)")
    print("- 30秒内: True (在去重窗口内，是重复)")
    print("- 70秒后: False (超过去重窗口，不是重复)")

if __name__ == "__main__":
    try:
        simulate_redis_notifications()
        demonstrate_time_window()
        print("\n🎉 演示完成！")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
