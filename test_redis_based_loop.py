#!/usr/bin/env python3
"""
测试基于Redis值的循环预约机制
"""

import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_spain_users_date_app_optimeized import RedisSubscriber, NotificationAppointmentManager
from user_manager import redis_client

def test_redis_based_deduplication():
    """测试基于Redis值的去重机制"""
    print("=== 测试基于Redis值的去重机制 ===\n")
    
    subscriber = RedisSubscriber()
    
    # 测试消息
    test_msg = {
        "centerCode": "GUANGZHOU",
        "visaType": "Tourism", 
        "isVIP": False,
        "dates": ["2025-07-25", "2025-07-26"]
    }
    
    msg_str = json.dumps(test_msg)
    notification_hash = subscriber._generate_notification_hash(msg_str)
    redis_key = f"spain-{notification_hash}"
    
    print(f"测试消息: {msg_str}")
    print(f"通知标识: {notification_hash}")
    print(f"Redis键: {redis_key}\n")
    
    # 测试1: Redis中没有值时应该被去重
    print("测试1: Redis中没有值")
    redis_client.delete(redis_key)  # 确保Redis中没有值
    is_duplicate = subscriber._is_duplicate_notification(notification_hash, time.time())
    print(f"是否重复: {is_duplicate} (应该是True)")
    assert is_duplicate == True, "Redis中没有值时应该被认为是重复通知"
    print("✓ 测试通过\n")
    
    # 测试2: Redis中有值时不应该被去重
    print("测试2: Redis中有值")
    redis_client.set(redis_key, msg_str, 300)  # 设置5分钟过期
    is_duplicate = subscriber._is_duplicate_notification(notification_hash, time.time())
    print(f"是否重复: {is_duplicate} (应该是False)")
    assert is_duplicate == False, "Redis中有值时不应该被认为是重复通知"
    print("✓ 测试通过\n")
    
    # 清理
    redis_client.delete(redis_key)

def test_redis_based_loop():
    """测试基于Redis值的循环预约"""
    print("=== 测试基于Redis值的循环预约 ===\n")
    
    # 准备测试数据
    test_notification = {
        "centerCode": "SHANGHAI",
        "visaType": "Business",
        "isVIP": True,
        "dates": ["2025-07-28", "2025-07-29"]
    }
    
    notification_id = "test-notification-001"
    redis_key = f"spain-{test_notification['centerCode']}-{test_notification['visaType']}-{test_notification['isVIP']}"
    
    print(f"测试通知: {test_notification}")
    print(f"Redis键: {redis_key}\n")
    
    # 设置Redis值
    redis_client.set(redis_key, json.dumps(test_notification), 60)
    
    # 创建通知管理器（但不实际执行预约）
    manager = NotificationAppointmentManager(notification_id, test_notification)
    
    # 模拟检查Redis值的逻辑
    print("模拟循环检查Redis值:")
    round_num = 0
    max_rounds = 5  # 最多检查5轮
    
    while round_num < max_rounds:
        redis_value = redis_client.get(redis_key)
        round_num += 1
        
        if not redis_value:
            print(f"第 {round_num} 轮: Redis值为空，停止循环")
            break
        else:
            print(f"第 {round_num} 轮: Redis值存在，继续处理")
            
        # 模拟在第3轮时删除Redis值
        if round_num == 3:
            print("  → 模拟删除Redis值")
            redis_client.delete(redis_key)
        
        time.sleep(1)  # 模拟处理时间
    
    print(f"循环结束，总共执行了 {round_num} 轮\n")
    
    # 清理
    redis_client.delete(redis_key)

def test_notification_key_generation():
    """测试通知键生成"""
    print("=== 测试通知键生成 ===\n")
    
    test_cases = [
        {
            "msg": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": False},
            "expected": "GUANGZHOU-Tourism-False"
        },
        {
            "msg": {"centerCode": "BEIJING", "visaType": "Business", "isVIP": True},
            "expected": "BEIJING-Business-True"
        },
        {
            "msg": {"centerCode": "SHANGHAI", "visaType": "Student", "isVIP": False},
            "expected": "SHANGHAI-Student-False"
        }
    ]
    
    subscriber = RedisSubscriber()
    
    for i, case in enumerate(test_cases, 1):
        msg_str = json.dumps(case["msg"])
        generated_key = subscriber._generate_notification_hash(msg_str)
        
        print(f"测试 {i}:")
        print(f"  消息: {case['msg']}")
        print(f"  期望键: {case['expected']}")
        print(f"  生成键: {generated_key}")
        print(f"  匹配: {generated_key == case['expected']}")
        
        assert generated_key == case["expected"], f"键生成不匹配: {generated_key} != {case['expected']}"
        print("  ✓ 测试通过\n")

if __name__ == "__main__":
    try:
        test_notification_key_generation()
        test_redis_based_deduplication()
        test_redis_based_loop()
        print("🎉 所有测试都通过了！")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
