import sys
import time
import threading
from queue import Queue
from copy import deepcopy
from urllib3.util.retry import Retry
from datetime import datetime

from spain_visa_login import user_login
from extension.logger import logger
from extension.session_manager import create_session
from tool import get_new_proxy, get_random_user_agent
from config import headers, default_centers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, spain_faker_scaning, spain_user_field
from user_manager import get_user_centers, del_user_from_redis, get_user_info
from spain_visa_update_profile import update_user_profile, confirm_email

DEFAULTWORKER = 4
# 设置重试策略
retries = Retry(
    total=3,  # 总重试次数
    backoff_factor=0.5,  # 退避因子，用于计算重试之间的间隔时间
    status_forcelist=[500, 502, 503, 504, 443],
)  # 需要重试的状态码

user_queue = Queue()


# @timmer
def keep_user_is_login(user):
    user = get_user_info(user)
    if not user:
        return None
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    user.pop("headers", None)
    user["queue_name"] = spain_faker_scaning
    # session = requests.session()
    # session.headers = header
    time_now = int(time.time())
    # 登录状态失效 或者 cookie快要过期   --- 假设cookie过期时间是20分钟
    if not user.get("is_login", False) or time_now - int(user.get("updateTime", 0)) > 900:
        proxy = get_new_proxy()
        if not proxy:
            return user

        session = create_session(proxy=proxy, header=header)
        isVIP = "VIP" if user.get("acceptVIP") == 1 else "Normal"
        logger.debug(f"##正在登录##{user.get('email')}, {user.get('centerCode')}, {isVIP}, update:{user.get('updateTime', '')}")
        flag_login, info_login = user_login(user, session)
        if not flag_login:
            if info_login == "deleted":
                del_user_from_redis(user)
                logger.error(f"##用户失效##{user.get('email')}, {user.get('centerCode')}, {isVIP}")
                return None
            user["is_login"] = False
            logger.error(f"##用户登录失败##: {user.get('email')}, {user.get('centerCode')} {isVIP} {user.get('visaTypeCode')}")
        else:
            logger.debug(f"##用户登录成功##: {user.get('email')}, {user.get('centerCode')} {isVIP} {user.get('visaTypeCode')}")
            cookie_dict = session.cookies.get_dict()
            user["cookies"] = cookie_dict
            user["proxy"] = proxy
            user["is_login"] = True
            user["updateTime"] = int(time.time())

        save_user_2_redis_queue(user)
        if not flag_login:
            return user

        # 更新用户信息
        if user["status"] == "login":
            flag_profile, res_profile = update_user_profile(user, session)
            if not flag_profile:
                logger.error(f"#注册# 更新失败:{user['email']} {user.get('visaTypeCode')}")
            else:
                logger.success(f"#注册# 更新成功:{user['email']} {user.get('visaTypeCode')}")
                user["status"] = "updated"
                save_user_2_redis_queue(user)

        # 邮箱双重确认
        if user["status"] == "updated":
            confirm_flag, res_confirm = confirm_email(user, session)
            if not confirm_flag:
                logger.error(f"#注册# 确认邮箱失败:{user['email']}")
            else:
                logger.success(f"#注册# 确认邮箱成功:{user['email']} {user.get('visaTypeCode')}")
                user["status"] = "update_appointment"
                save_user_2_redis_queue(user)
    else:
        pass
        # logger.debug(f"##保持登录##user:{user.get('email')}, login:{user.get('is_login')}, update:{user.get('updateTime')}")
    return user


def work_scan_appointment_only():
    while True:
        if user_queue.empty():
            time.sleep(2)
            continue
        else:
            user = user_queue.get()
            user_exist = keep_user_is_login(user)
            # 删除掉的用户不加回来了
            if user_exist:
                user_queue.put(user)
            time.sleep(1)
            user_queue.task_done()


def start_scaning(thread_count=10):
    logger.info("#扫号#用户登录....")
    threads = []
    # 使用 for 循环创建多线程并传入参数
    for _ in range(thread_count):
        thread = threading.Thread(target=work_scan_appointment_only, daemon=True)
        threads.append(thread)
        thread.start()

        # 等待所有线程完成
        # for thread in threads:
        #     thread.join()
    in_scaning_centers = []
    while True:
        user_centers = [] # get_user_centers()
        all_faker_users = get_users_with_queue_name()
        spain_users = get_users_with_queue_name(spain_user_field)
        for u in spain_users:
            center_tag = u["centerCode"]
            if center_tag not in in_scaning_centers:
                user_centers.append(center_tag) # 本次新增的
        user_centers = list(set(user_centers))
        if len(user_centers) == 0:
            time.sleep(10)
            continue
        # current_centers = ["CHONGQING"]
        
        users_scan = []
        for center in user_centers:
            if center not in in_scaning_centers:
                in_scaning_centers.append(center)
                user_ = list(filter(lambda x: x["centerCode"].upper() == center, all_faker_users))
                users_scan = users_scan + user_
            else:
                continue
        if is_after_8pm():
            logger.warning("登录所有虚拟用户账号，保活")
            users_scan = all_faker_users

        if len(users_scan) <= 0:
            time.sleep(10)
            continue

        for user in users_scan:
            user_queue.put(user)

        time.sleep(5)

def is_after_8pm():
    """
    判断当前时间是否是晚上八点之后（包括 20:00:00）。

    返回:
        bool: 如果当前时间是晚上八点之后，返回 True；否则返回 False。
    """
    # 获取当前时间
    now = datetime.now()

    # 获取当前小时和分钟
    current_hour = now.hour
    current_minute = now.minute

    # 判断是否是晚上八点之后
    if current_hour > 20 or (current_hour == 20 and current_minute >= 30):
        return True
    else:
        return False


if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else 10
    start_scaning(thread_count)

