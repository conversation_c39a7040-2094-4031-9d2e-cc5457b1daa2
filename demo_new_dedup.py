#!/usr/bin/env python3
"""
演示基于centerCode、visaType、isVIP的去重机制
"""

import json
from main_spain_users_date_app_optimeized import RedisSubscriber

def demo_new_deduplication():
    """演示新的去重逻辑"""
    print("=== 基于三字段的去重机制演示 ===\n")
    
    subscriber = RedisSubscriber()
    
    # 基础通知
    base_msg = {
        "centerCode": "GUANGZHOU",
        "visaType": "Tourism",
        "isVIP": False,
        "dates": ["2025-07-25", "2025-07-26"]
    }
    
    print("基础通知:")
    print(f"  {json.dumps(base_msg, ensure_ascii=False)}")
    base_hash = subscriber._generate_notification_hash(json.dumps(base_msg))
    print(f"  Hash: {base_hash}\n")
    
    # 测试1: 相同的三个关键字段，不同日期
    msg1 = base_msg.copy()
    msg1["dates"] = ["2025-08-01", "2025-08-02", "2025-08-03"]  # 完全不同的日期
    hash1 = subscriber._generate_notification_hash(json.dumps(msg1))
    
    print("测试1 - 不同日期，相同关键字段:")
    print(f"  {json.dumps(msg1, ensure_ascii=False)}")
    print(f"  Hash: {hash1}")
    print(f"  是否重复: {base_hash == hash1} ✓\n")
    
    # 测试2: 不同地区
    msg2 = base_msg.copy()
    msg2["centerCode"] = "BEIJING"
    hash2 = subscriber._generate_notification_hash(json.dumps(msg2))
    
    print("测试2 - 不同地区:")
    print(f"  {json.dumps(msg2, ensure_ascii=False)}")
    print(f"  Hash: {hash2}")
    print(f"  是否重复: {base_hash == hash2} (应该是False) ✓\n")
    
    # 测试3: 不同签证类型
    msg3 = base_msg.copy()
    msg3["visaType"] = "Business"
    hash3 = subscriber._generate_notification_hash(json.dumps(msg3))
    
    print("测试3 - 不同签证类型:")
    print(f"  {json.dumps(msg3, ensure_ascii=False)}")
    print(f"  Hash: {hash3}")
    print(f"  是否重复: {base_hash == hash3} (应该是False) ✓\n")
    
    # 测试4: 不同VIP状态
    msg4 = base_msg.copy()
    msg4["isVIP"] = True
    hash4 = subscriber._generate_notification_hash(json.dumps(msg4))
    
    print("测试4 - 不同VIP状态:")
    print(f"  {json.dumps(msg4, ensure_ascii=False)}")
    print(f"  Hash: {hash4}")
    print(f"  是否重复: {base_hash == hash4} (应该是False) ✓\n")
    
    # 测试5: 添加额外字段不影响hash
    msg5 = base_msg.copy()
    msg5["extraField"] = "这个字段不影响hash"
    msg5["anotherField"] = 12345
    hash5 = subscriber._generate_notification_hash(json.dumps(msg5))
    
    print("测试5 - 添加额外字段:")
    print(f"  {json.dumps(msg5, ensure_ascii=False)}")
    print(f"  Hash: {hash5}")
    print(f"  是否重复: {base_hash == hash5} ✓\n")
    
    print("=== 去重逻辑总结 ===")
    print("✓ 只有 centerCode、visaType、isVIP 三个字段影响去重")
    print("✓ dates 字段不影响去重判断")
    print("✓ 额外字段不影响去重判断")
    print("✓ 相同的三个关键字段 = 重复通知")

def demo_practical_scenario():
    """演示实际使用场景"""
    print("\n\n=== 实际使用场景演示 ===\n")
    
    subscriber = RedisSubscriber()
    
    scenarios = [
        {
            "name": "广州旅游签证普通号 - 第一次放号",
            "msg": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": False, "dates": ["2025-07-25"]}
        },
        {
            "name": "广州旅游签证普通号 - 增加更多日期",
            "msg": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": False, "dates": ["2025-07-25", "2025-07-26", "2025-07-27"]}
        },
        {
            "name": "广州旅游签证普通号 - 完全不同的日期",
            "msg": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": False, "dates": ["2025-08-01", "2025-08-02"]}
        },
        {
            "name": "广州旅游签证VIP号 - 相同日期",
            "msg": {"centerCode": "GUANGZHOU", "visaType": "Tourism", "isVIP": True, "dates": ["2025-07-25"]}
        },
        {
            "name": "广州商务签证普通号 - 相同日期",
            "msg": {"centerCode": "GUANGZHOU", "visaType": "Business", "isVIP": False, "dates": ["2025-07-25"]}
        }
    ]
    
    hashes = {}
    
    for i, scenario in enumerate(scenarios, 1):
        msg_str = json.dumps(scenario["msg"])
        hash_value = subscriber._generate_notification_hash(msg_str)
        
        print(f"{i}. {scenario['name']}")
        print(f"   消息: {msg_str}")
        print(f"   Hash: {hash_value}")
        
        # 检查是否与之前的hash重复
        duplicate_with = None
        for prev_name, prev_hash in hashes.items():
            if prev_hash == hash_value:
                duplicate_with = prev_name
                break
        
        if duplicate_with:
            print(f"   ⚠️  与 '{duplicate_with}' 重复，会被去重")
        else:
            print(f"   ✓ 唯一通知，会被处理")
        
        hashes[scenario['name']] = hash_value
        print()

if __name__ == "__main__":
    try:
        demo_new_deduplication()
        demo_practical_scenario()
        print("\n🎉 演示完成！")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
