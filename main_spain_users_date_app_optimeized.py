import requests
import sys
import time
import threading
import json
from queue import Queue, Empty
from copy import deepcopy
from typing import Dict, List

# from spain_visa_appointment_date_open import book_appointment
from spain_visa_date_appointment import date_appointment
from extension.logger import logger
from tool import get_random_user_agent, str_2_timestamp
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, subscribe_redis_msg, get_user_info
from user_manager import get_user_date_status, set_user_date_status, redis_client

# 全局配置
spain_user_field = "spainUserDatas"
REDIS_FIELD_PREFIX = "spain-"  # Redis字段前缀
MONITOR_INTERVAL = 2  # 监控间隔（秒）
WORKER_THREAD_COUNT = 5  # 每个地区的工作线程数
MAX_RETRY_COUNT = 3  # 最大重试次数

# 全局管理器
region_managers: Dict[str, 'RegionAppointmentManager'] = {}
field_monitor = None


class RedisFieldMonitor:
    """Redis字段监控器，持续监控放号信息"""

    def __init__(self):
        self.running = False
        self.monitor_thread = None
        self.last_field_values = {}

    def start_monitoring(self):
        """开始监控Redis字段"""
        if self.running:
            return
        
        # subscribe_redis_msg(self._handle_change)
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("Redis字段监控器已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("Redis字段监控器已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                self._check_appointment_fields()
                time.sleep(MONITOR_INTERVAL)
            except Exception as e:
                logger.error(f"Redis字段监控异常: {e}")
                time.sleep(MONITOR_INTERVAL)

    def _check_appointment_fields(self):
        """检查预约字段变化"""
        try:
            # 获取所有预约相关的Redis字段
            pattern = f"{REDIS_FIELD_PREFIX}*"
            keys = redis_client.client.keys(pattern)

            for key in keys:
                if key.startswith(f"{REDIS_FIELD_PREFIX}") and "-" in key:
                    # 过滤掉用户状态字段（包含护照号的字段）
                    if self._is_user_status_field(key):
                        continue

                    try:
                        value = redis_client.get(key)
                        if value and value != self.last_field_values.get(key):
                            self.last_field_values[key] = value
                            self._handle_field_change(key, value)
                    except Exception as e:
                        logger.error(f"检查字段 {key} 时出错: {e}")

        except Exception as e:
            logger.error(f"检查预约字段时出错: {e}")
    

    def _is_user_status_field(self, field_key: str) -> bool:
        """判断是否为用户状态字段"""
        # 用户状态字段格式: spain-地区-护照号
        # 预约信息字段格式: spain-地区-签证类型-VIP类型
        parts = field_key.split("-")
        if len(parts) >= 3:
            # 如果第三部分看起来像护照号（包含字母和数字），则认为是用户状态字段
            third_part = parts[2]
            if len(third_part) > 5 and any(c.isalpha() for c in third_part) and any(c.isdigit() for c in third_part):
                return True
        return False
        
    def _handle_field_change(self, field_key: str, field_value: str):
        """处理字段变化"""
        try:
            appointment_info = json.loads(field_value)
            center_code = appointment_info.get("centerCode")
            visa_type = appointment_info.get("visaType")

            if center_code and visa_type:
                # tag = f"{center_code}-{visa_type}"
                # 获取或创建地区管理器
                if field_key not in region_managers:
                    region_managers[field_key] = RegionAppointmentManager(field_key)
                    region_managers[field_key].start()

                # 通知地区管理器更新预约信息
                region_managers[field_key].update_appointment_info(appointment_info)
                logger.info(f"检测到 {field_key} 地区放号信息更新: {appointment_info}")

        except json.JSONDecodeError:
            # 忽略非JSON格式的字段值
            logger.debug(f"忽略非JSON字段 {field_key}: {field_value}")
        except Exception as e:
            logger.error(f"处理字段变化时出错 {field_key}: {e}")


class RegionAppointmentManager:
    """地区预约管理器，负责单个地区的持续预约"""

    def __init__(self, center_code: str):
        self.center_code = center_code
        self.running = False
        self.appointment_info = None
        self.user_queue = Queue()
        self.worker_threads = []
        self.last_update_time = 0
        self.stats = {
            'processed_users': 0,
            'successful_appointments': 0,
            'failed_appointments': 0
        }

    def start(self):
        """启动地区管理器"""
        if self.running:
            return

        self.running = True

        # 启动工作线程
        for i in range(WORKER_THREAD_COUNT):
            thread = threading.Thread(
                target=self._worker_loop,
                args=(i,),
                daemon=True,
                name=f"Worker-{self.center_code}-{i}"
            )
            thread.start()
            self.worker_threads.append(thread)

        # 启动用户队列更新线程
        update_thread = threading.Thread(
            target=self._update_user_queue_loop,
            daemon=True,
            name=f"UserUpdater-{self.center_code}"
        )
        update_thread.start()
        self.worker_threads.append(update_thread)

        logger.info(f"地区 {self.center_code} 预约管理器已启动，工作线程数: {WORKER_THREAD_COUNT}")

    def stop(self):
        """停止地区管理器"""
        self.running = False
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        logger.info(f"地区 {self.center_code} 预约管理器已停止")

    def update_appointment_info(self, appointment_info: dict):
        """更新预约信息"""
        self.appointment_info = appointment_info
        self.last_update_time = time.time()
        logger.info(f"地区 {self.center_code} 预约信息已更新: {appointment_info}")

    def _update_user_queue_loop(self):
        """持续更新用户队列"""
        while self.running:
            try:
                if self.appointment_info:
                    self._refresh_user_queue()
                time.sleep(MONITOR_INTERVAL * 2)  # 用户队列更新频率稍低
            except Exception as e:
                logger.error(f"地区 {self.center_code} 用户队列更新异常: {e}")
                time.sleep(MONITOR_INTERVAL)

    def _refresh_user_queue(self):
        """刷新用户队列"""
        try:
            appointment_info = self.appointment_info
            if not appointment_info:
                return

            center_code = appointment_info.get("centerCode")
            visa_type = appointment_info.get("visaType")
            is_vip = appointment_info.get("isVIP", False)
            open_days = appointment_info.get("dates", [])

            if not center_code or not visa_type:
                return

            # 获取所有用户
            all_users = get_users_with_queue_name(spain_user_field)

            # 筛选等待预约的用户
            waiting_users = list(filter(
                lambda u: (
                    u["status"] == "update_appointment" and
                    u.get("centerCode") == center_code and
                    u.get("visaTypeCode") == visa_type
                ),
                all_users
            ))

            # VIP筛选
            if is_vip:
                waiting_users = list(filter(lambda u: u["acceptVIP"] == 1, waiting_users))

            # 筛选期望日期匹配的用户
            eligible_users = []
            for user in waiting_users:
                if self._date_available(user, open_days):
                    # 检查用户是否已在队列中
                    user_key = f"{user.get('centerCode')}-{user.get('passportNO')}"
                    if not self._is_user_in_queue(user_key):
                        user["dateVIP"] = is_vip
                        eligible_users.append(user)

            # 将符合条件的用户加入队列
            for user in eligible_users:
                try:
                    self.user_queue.put(user, block=False)
                    self.stats['processed_users'] += 1
                except Exception:
                    pass  # 队列满时忽略

            if eligible_users:
                logger.info(f"地区 {center_code} 新增 {len(eligible_users)} 个符合条件的用户到预约队列")

        except Exception as e:
            logger.error(f"地区 {self.center_code} 刷新用户队列时出错: {e}")

    def _is_user_in_queue(self, user_key: str) -> bool:
        """检查用户是否已在队列中（简单实现）"""
        # 这里可以实现更复杂的去重逻辑
        return False

    def _date_available(self, user_info: dict, all_dates: List[str]) -> bool:
        """检查用户期望日期是否可用"""
        if len(all_dates) == 0:
            return True

        start_date = user_info.get("startDate", "").replace("/", "-")
        end_date = user_info.get("endDate", "").replace("/", "-")

        # 判断是否有可预约日期
        for date_item in all_dates:
            try:
                if str_2_timestamp(start_date) <= str_2_timestamp(date_item) <= str_2_timestamp(end_date):
                    return True
            except Exception:
                continue
        return False

    def _worker_loop(self, worker_id: int):
        """工作线程循环"""
        logger.info(f"地区 {self.center_code} 工作线程 {worker_id} 已启动")

        while self.running:
            try:
                # 从队列获取用户
                try:
                    user = self.user_queue.get(timeout=1)
                except Empty:
                    continue

                # 获取最新用户信息
                fresh_user = get_user_info(user)
                if not fresh_user:
                    self.user_queue.task_done()
                    continue

                # 保持VIP标记
                fresh_user["dateVIP"] = user.get("dateVIP", False)

                # 检查用户预约状态
                if get_user_date_status(fresh_user):
                    self.user_queue.task_done()
                    continue

                # 设置预约中状态
                set_user_date_status(fresh_user, True)

                try:
                    # 执行预约
                    flag_book, _ = self._execute_appointment(fresh_user)

                    if flag_book:
                        self.stats['successful_appointments'] += 1
                        logger.success(f"地区 {self.center_code} 工作线程 {worker_id} 预约成功: {fresh_user.get('email')}")
                    else:
                        self.stats['failed_appointments'] += 1

                finally:
                    # 重置预约状态
                    set_user_date_status(fresh_user, False)

                self.user_queue.task_done()

            except Exception as e:
                logger.error(f"地区 {self.center_code} 工作线程 {worker_id} 异常: {e}")
                time.sleep(1)

        logger.info(f"地区 {self.center_code} 工作线程 {worker_id} 已停止")

    def _execute_appointment(self, user: dict):
        """执行预约操作"""
        return date_job(user)

    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['queue_size'] = self.user_queue.qsize()
        stats['last_update_time'] = self.last_update_time
        return stats


# @timmer
def date_job(user):
    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()

    # 登录状态为否 跳过等待10s用户登陆
    # isVIP = user["dateVIP"]
    # user = get_user_info(user)
    # user["dateVIP"] = isVIP
    if not user.get("is_login", False):
        logger.error(f"##预约## 用户未登录:{user.get('chnname')}-{user.get('email')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}")
        time.sleep(0.1)
        return None, None

    logger.info(f"##预约## 开始预约: {user.get('chnname')}-{user.get('email')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}-{user.get('dateVIP')}")

    cookie_dict = user["cookies"]
    proxy = user["proxy"]
    proxy_dict = {"http": proxy, "https": proxy}
    session = session = requests.session()
    session.proxies.update(proxy_dict)
    session.cookies.update(cookie_dict)
    session.headers.update(header)  # 将重试策略应用到会话对象

    # 查询预约号
    flag_book, done_job = date_appointment(user, session)
    if not flag_book:
        user["is_login"] = False
        save_user_2_redis_queue(user)
    return flag_book, done_job


def start_continuous_appointment_system():
    """启动持续预约系统"""
    global field_monitor

    logger.info("启动持续预约系统...")

    # 初始化重置用户的预约状态
    all_users = get_users_with_queue_name(spain_user_field)
    for user in all_users:
        set_user_date_status(user, False)

    # 启动Redis字段监控器
    field_monitor = RedisFieldMonitor()
    field_monitor.start_monitoring()

    logger.info("持续预约系统启动完成")

    # 保持主线程运行
    try:
        while True:
            time.sleep(60)
            # 定期输出统计信息
            _print_system_stats()
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭系统...")
        stop_continuous_appointment_system()


def stop_continuous_appointment_system():
    """停止持续预约系统"""
    global field_monitor, region_managers

    logger.info("正在停止持续预约系统...")

    # 停止字段监控器
    if field_monitor:
        field_monitor.stop_monitoring()

    # 停止所有地区管理器
    for center_code, manager in region_managers.items():
        manager.stop()

    region_managers.clear()
    logger.info("持续预约系统已停止")


def _print_system_stats():
    """打印系统统计信息"""
    if not region_managers:
        return

    logger.info("=== 系统统计信息 ===")
    for center_code, manager in region_managers.items():
        stats = manager.get_stats()
        logger.info(f"地区 {center_code}: 队列大小={stats['queue_size']}, "
                   f"处理用户={stats['processed_users']}, "
                   f"成功预约={stats['successful_appointments']}, "
                   f"失败预约={stats['failed_appointments']}")


# 保留旧的函数以兼容现有调用
def date_avaliable(user_info, all_dates):
    if len(all_dates) == 0:
        return True
    start_date = user_info.get("startDate", "").replace("/", "-")
    end_date = user_info.get("endDate", "").replace("/", "-")
    # 判断出是否有可预约日期
    date_avaliable_flag = False
    for date_item in all_dates:
        if str_2_timestamp(start_date) <= str_2_timestamp(date_item) <= str_2_timestamp(end_date):
            date_avaliable_flag = True
            break
    return date_avaliable_flag


# 兼容旧版本的函数，保留用于向后兼容
def start_date_appointment(channel, msg_str, thread_count=20):
    """兼容旧版本的预约启动函数"""
    try:
        msg_json = json.loads(msg_str)
        center_code = msg_json.get("centerCode")

        logger.info(f"##兼容模式## 收到预约消息: {msg_json}")

        # 如果持续预约系统已启动，则通过新系统处理
        if field_monitor and field_monitor.running:
            # 创建临时Redis字段来触发新系统
            field_key = f"{REDIS_FIELD_PREFIX}{center_code}-{msg_json.get('visaType', 'Unknown')}-{1 if msg_json.get('isVIP') else 2}"
            redis_client.set(field_key, msg_str, 60 * 5)  # 5分钟过期
            logger.info(f"##兼容模式## 已转发到持续预约系统")
            return []
        else:
            # 使用旧的单次处理模式
            return _legacy_start_date_appointment(channel, msg_str, thread_count)

    except Exception as e:
        logger.error(f"预约启动异常: {e}")
        return []


def _legacy_start_date_appointment(channel, msg_str, thread_count=20):
    """旧版本的预约处理逻辑"""
    msg_json = json.loads(msg_str)
    center_code = msg_json.get("centerCode")  # 放号地区
    isVIP = msg_json.get("isVIP", False)  # 是否是是VIP号
    open_days = msg_json.get("dates")  # 开放日期数组
    visaType = msg_json.get("visaType")

    logger.info(f"##预约#收到redis放号通知: 开始预约 {center_code} 地区用户, visaType: {visaType}, VIP:{isVIP}, 可约日期:{open_days}")

    # 临时队列用于单次处理
    temp_queue = Queue()

    all_users = get_users_with_queue_name(spain_user_field)
    # 筛选等待预约的客户
    users_wating_appointment = list(filter(
        lambda u: u["status"] == "update_appointment" and
                 u.get("centerCode") == center_code and
                 u.get("visaTypeCode") == visaType,
        all_users
    ))

    # VIP等候室只预约接受VIP的，普通号全部预约
    if isVIP:
        users_wating_appointment = list(filter(lambda u: u["acceptVIP"] == 1, users_wating_appointment))

    if len(users_wating_appointment) <= 0:
        logger.info(f"##预约#{center_code} 地区无用户等待预约")
        return []

    # 把期望日期有的用户筛选出来加入队列预约
    for user in users_wating_appointment:
        if date_avaliable(user, open_days):
            user["dateVIP"] = isVIP  # 增加一个标记 表示这次预约的是否是VIP
            temp_queue.put(user)

    if temp_queue.empty():
        logger.info(f"##预约#{center_code} 地区无用户期望的日期")
        return []

    # 创建临时工作线程
    def temp_worker():
        while not temp_queue.empty():
            try:
                user_old = temp_queue.get(timeout=1)
                isVIP = user_old["dateVIP"]
                user = get_user_info(user_old)
                if user:
                    user["dateVIP"] = isVIP
                    if not get_user_date_status(user):
                        set_user_date_status(user, True)
                        try:
                            date_job(user)
                        finally:
                            set_user_date_status(user, False)
                temp_queue.task_done()
            except Empty:
                break
            except Exception as e:
                logger.error(f"临时工作线程异常: {e}")

    # 启动临时工作线程
    threads = []
    for _ in range(thread_count):
        thread = threading.Thread(target=temp_worker, daemon=True)
        threads.append(thread)
        thread.start()

    return threads


def listen_redis_pub():
    """兼容旧版本的Redis订阅模式"""
    logger.info("开启redis sub 订阅（兼容模式）")

    # 初始化重置用户的预约状态
    all_users = get_users_with_queue_name(spain_user_field)
    for user in all_users:
        set_user_date_status(user, False)

    subscribe_redis_msg(start_date_appointment)
    while True:
        logger.debug("redis_sub 订阅中...")
        time.sleep(600)


def date_single_users(centerCode="BEIJING"):
    """单地区测试模式"""
    info = {
        "centerCode": centerCode,
        "dates": [
            "2025-07-18",
            "2025-07-21",
            "2025-07-25",
        ],
        "isVIP": True,
        "visaType": "Business",
    }
    str_info = json.dumps(info)

    while True:
        threads = start_date_appointment("channel", str_info, 1)
        for t in threads:
            t.join()
        time.sleep(5)


def main_continuous_mode():
    """持续预约模式主函数"""
    logger.info("启动持续预约模式")
    start_continuous_appointment_system()


def main_legacy_mode():
    """兼容模式主函数"""
    logger.info("启动兼容模式")
    listen_redis_pub()


def main_test_mode(center_code="GUANGZHOU"):
    """测试模式主函数"""
    logger.info(f"启动测试模式，地区: {center_code}")
    date_single_users(center_code)


if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]

    if len(args) == 0:
        # 默认启动持续预约模式
        main_continuous_mode()
    elif args[0] == "continuous":
        # 持续预约模式
        main_continuous_mode()
    elif args[0] == "legacy":
        # 兼容模式（旧版Redis订阅）
        main_legacy_mode()
    elif args[0] == "test":
        # 测试模式
        center_code = args[1] if len(args) > 1 else "GUANGZHOU"
        main_test_mode(center_code)
    else:
        # 兼容旧版本参数（线程数）
        try:
            thread_count = int(args[0])
            if thread_count > 0:
                main_legacy_mode()
            else:
                main_test_mode()
        except ValueError:
            logger.error(f"未知参数: {args[0]}")
            logger.info("使用方法:")
            logger.info("  python main_spain_users_date_appointment.py continuous  # 持续预约模式")
            logger.info("  python main_spain_users_date_appointment.py legacy     # 兼容模式")
            logger.info("  python main_spain_users_date_appointment.py test [地区] # 测试模式")
