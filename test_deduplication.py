#!/usr/bin/env python3
"""
测试Redis通知去重功能
"""

import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_spain_users_date_app_optimeized import RedisSubscriber

def test_notification_deduplication():
    """测试通知去重功能"""
    print("开始测试Redis通知去重功能...")
    
    # 创建订阅器实例
    subscriber = RedisSubscriber()
    
    # 测试消息
    test_msg = {
        "centerCode": "GUANGZHOU",
        "visaType": "Tourism",
        "isVIP": False,
        "dates": ["2025-07-25", "2025-07-26", "2025-07-27"]
    }
    
    msg_str = json.dumps(test_msg)
    
    print(f"测试消息: {msg_str}")
    
    # 测试1: 生成hash
    hash1 = subscriber._generate_notification_hash(msg_str)
    hash2 = subscriber._generate_notification_hash(msg_str)
    print(f"Hash1: {hash1}")
    print(f"Hash2: {hash2}")
    assert hash1 == hash2, "相同消息应该生成相同的hash"
    print("✓ Hash生成测试通过")
    
    # 测试2: 不同消息生成不同hash
    test_msg2 = test_msg.copy()
    test_msg2["centerCode"] = "BEIJING"
    msg_str2 = json.dumps(test_msg2)
    hash3 = subscriber._generate_notification_hash(msg_str2)
    assert hash1 != hash3, "不同消息应该生成不同的hash"
    print("✓ 不同消息Hash测试通过")
    
    # 测试3: 日期不同但其他字段相同应该生成相同hash
    test_msg3 = test_msg.copy()
    test_msg3["dates"] = ["2025-08-01", "2025-08-02"]  # 完全不同的日期
    msg_str3 = json.dumps(test_msg3)
    hash4 = subscriber._generate_notification_hash(msg_str3)
    assert hash1 == hash4, "日期不同但centerCode、visaType、isVIP相同应该生成相同的hash"
    print("✓ 日期不影响hash测试通过")

    # 测试4: VIP状态不同应该生成不同hash
    test_msg4 = test_msg.copy()
    test_msg4["isVIP"] = True  # 改变VIP状态
    msg_str4 = json.dumps(test_msg4)
    hash5 = subscriber._generate_notification_hash(msg_str4)
    assert hash1 != hash5, "VIP状态不同应该生成不同的hash"
    print("✓ VIP状态影响hash测试通过")
    
    # 测试4: 重复检测
    current_time = time.time()
    
    # 第一次处理
    is_dup1 = subscriber._is_duplicate_notification(hash1, current_time)
    assert not is_dup1, "第一次处理不应该被认为是重复"
    subscriber.processed_notifications[hash1] = current_time
    print("✓ 首次处理测试通过")
    
    # 立即重复处理
    is_dup2 = subscriber._is_duplicate_notification(hash1, current_time + 1)
    assert is_dup2, "短时间内重复处理应该被检测为重复"
    print("✓ 重复检测测试通过")
    
    # 超过时间窗口后处理
    future_time = current_time + 70  # 超过60秒窗口
    is_dup3 = subscriber._is_duplicate_notification(hash1, future_time)
    assert not is_dup3, "超过时间窗口后不应该被认为是重复"
    print("✓ 时间窗口测试通过")
    
    # 测试5: 清理过期记录
    subscriber.processed_notifications[hash1] = current_time
    subscriber.processed_notifications[hash3] = current_time - 70  # 过期记录
    
    subscriber._cleanup_expired_notifications(current_time)
    
    assert hash1 in subscriber.processed_notifications, "未过期记录应该保留"
    assert hash3 not in subscriber.processed_notifications, "过期记录应该被清理"
    print("✓ 清理过期记录测试通过")
    
    print("\n所有测试通过！✓")

def test_malformed_message():
    """测试格式错误的消息"""
    print("\n测试格式错误的消息处理...")
    
    subscriber = RedisSubscriber()
    
    # 测试无效JSON
    invalid_json = "这不是一个有效的JSON"
    hash1 = subscriber._generate_notification_hash(invalid_json)
    print(f"无效JSON的Hash: {hash1}")
    
    # 测试缺少字段的JSON
    incomplete_msg = json.dumps({"centerCode": "GUANGZHOU"})
    hash2 = subscriber._generate_notification_hash(incomplete_msg)
    print(f"不完整消息的Hash: {hash2}")
    
    assert hash1 != hash2, "不同的错误消息应该生成不同的hash"
    print("✓ 错误消息处理测试通过")

if __name__ == "__main__":
    try:
        test_notification_deduplication()
        test_malformed_message()
        print("\n🎉 所有测试都通过了！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
